## 架构图（Architecture Diagrams）

### 系统架构总览

```mermaid
graph TD
  subgraph "Inputs"
    A["RTK Raw (NMEA/CSV)"]
    B["Perception Raw (JSON/CSV)"]
  end
  A --> C["RawDataPreprocessor"]
  B --> C
  C --> D["Standard CSV"]
  D --> E["DataLoader & TimeSync"]
  E --> F["SimpleDistanceMatcher"]
  F --> G["Corridor/ROI Filter"]
  G --> H["Build Segments"]
  H --> I["Scoring (Unified/F1/Traditional)"]
  I --> J["Core Chain"]
  J --> K["AnomalyAnalysisManager"]
  K --> L["OutputGenerator (CSV/JSON)"]
  L --> M["AccuracyReportGenerator (Jinja2 HTML)"]
  L --> N["BatchReportGenerator (Jinja2 HTML)"]
  O["ConfigLoader (profiles)"] --> E
  O --> F
  O --> K
  O --> L
```

### 端到端匹配流程（顺序图）

```mermaid
sequenceDiagram
  participant User as User/CLI
  participant Main as main.py
  participant TM as trajectory_matcher.run_matching
  participant Prep as RawDataPreprocessor
  participant DL as DataLoader
  participant Match as SimpleDistanceMatcher
  participant AA as AnomalyAnalysisManager
  participant OG as OutputGenerator
  participant AR as AccuracyReportGenerator

  User->>Main: rtk, perception, config, --output-dir
  Main->>TM: run_matching(args)
  alt skip_preprocess is false
    TM->>Prep: detect & preprocess (RTK/Perception)
    Prep-->>TM: rtk.csv, per.csv
  else
    TM-->>TM: use original CSVs
  end
  TM->>DL: load & sync time
  DL-->>TM: RTKPoints, PerceptionPoints
  TM->>Match: filter (Corridor/ROI), build segments
  Match-->>TM: segments, core chain
  TM->>AA: analyze anomalies
  AA-->>TM: events & summary
  TM->>OG: write matched.csv & diagnostic.json
  OG-->>TM: file paths
  TM->>AR: render accuracy HTML (optional)
  AR-->>TM: report.html
  TM-->>Main: outputs summary
  Main-->>User: paths & report links
```

以上图示与 `DESIGN.md` 一致，可用于快速理解数据流与模块边界。


