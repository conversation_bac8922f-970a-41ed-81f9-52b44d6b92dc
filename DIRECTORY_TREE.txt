车路协同轨迹匹配系统 - 目录树结构
=====================================

📁 项目根目录/
│
├── 📋 主要文档
│   ├── README.md                           # 项目主要说明
│   ├── CHANGELOG.md                        # 版本更新日志
│   ├── DOCUMENTATION.md                    # 完整文档索引
│   ├── QUICK_START.md                      # 快速开始指南
│   ├── BATCH_PROCESSING_GUIDE.md           # 批量处理指南
│   ├── PROJECT_STRUCTURE.md               # 项目结构说明
│   ├── DIRECTORY_TREE.txt                 # 目录树（本文件）
│   └── requirements.txt                    # Python依赖包
│
├── 🎯 主程序入口
│   ├── main.py                            # 单文件处理主程序 ⭐
│   ├── batch_simple.py                    # 串行批量处理 ⭐
│   └── batch_parallel_linux_optimized.py  # 并行批量处理 ⭐
│
├── 🔧 工具目录 (tools/)
│   └── check_config.py                    # 配置检查工具
│
├── 📖 中文文档
│   ├── 新手快速上手指南.md                # 中文快速入门
│   ├── 文档使用指南.md                    # 中文文档指南
│   ├── 批量处理HTML报告使用指南.md         # 中文报告指南
│   └── 项目完成总结.md                    # 项目总结
│
├── 🔧 核心代码模块 (core/)
│   ├── __init__.py                        # 模块初始化
│   ├── trajectory_matcher.py             # 轨迹匹配核心算法 ⭐
│   ├── preprocessor.py                    # 数据预处理模块 ⭐
│   ├── data_utils.py                      # 数据工具函数
│   ├── corridor_filter.py                # 走廊过滤算法 ⭐
│   ├── simple_distance_matcher.py        # 简单距离匹配器
│   ├── gap_analyzer.py                    # 间隙分析器
│   ├── config_loader.py                   # 配置加载器 ⭐
│   ├── output_generator.py               # 输出生成器
│   ├── report_generator.py               # 报告生成器 ⭐
│   ├── batch_report_generator.py         # 批量报告生成器 ⭐
│   │
│   └── 🔍 异常分析子模块 (anomaly_analysis/)
│       ├── __init__.py                    # 异常分析初始化
│       ├── analysis_manager.py           # 异常分析管理器 ⭐
│       ├── base_analyzer.py              # 分析器基类
│       ├── result_summarizer.py          # 结果汇总器
│       ├── accuracy_analyzer.py          # 精度分析器
│       ├── id_switch_analyzer.py         # ID切换分析器
│       ├── missing_gap_analyzer.py       # 漏检间隙分析器
│       ├── split_detector.py             # 轨迹分裂检测器
│       ├── time_jump_analyzer.py         # 时间跳跃分析器
│       └── trajectory_quality_analyzer.py # 轨迹质量分析器
│
├── ⚙️ 配置文件 (config/)
│   ├── unified_config.json               # 统一配置文件 ⭐
│   ├── default.json                      # 默认配置文件
│   └── batch_config.json                 # 批量处理配置
│
├── 📊 数据文件 (data/)
│   ├── 🛣️ RTK轨迹数据
│   │   ├── 31.log ~ 36.log               # RTK轨迹数据文件
│   │   └── rtk_part001.txt ~ rtk_part009.txt # RTK分段数据
│   │
│   ├── 🚗 感知数据
│   │   ├── save_1753355915725.txt        # 感知数据样本 ⭐
│   │   ├── AJ06993PAJ00*.txt             # 感知数据集合
│   │   ├── ST101211100*.txt              # 感知数据集合
│   │   └── first.txt, second.txt         # 测试数据
│   │
│   └── 📋 批量配置
│       └── batch.csv                     # 批量处理任务配置 ⭐
│
├── 🧪 测试代码 (tests/)
│   ├── 🔬 单元测试
│   │   ├── test_speed_unit_conversion.py     # 速度单位转换测试 ⭐
│   │   ├── test_unified_scoring_regression.py # 统一评分回归测试 ⭐
│   │   ├── test_accuracy_report_integration.py # 精度报告集成测试 ⭐
│   │   └── test_performance_optimization.py   # 性能优化测试
│   │
│   └── 📊 性能测试
│       ├── performance_benchmark_test.py     # 性能基准测试
│       ├── real_data_performance_test.py     # 真实数据性能测试
│       ├── accuracy_comparison_test.py       # 精度对比测试
│       └── parameter_tuning.py               # 参数调优测试
│
├── 📄 模板文件 (templates/)
│   ├── accuracy_report_template.html     # 精度分析报告模板 ⭐
│   └── batch_summary_template.html       # 批量汇总报告模板 ⭐
│
├── 📚 详细文档 (docs/)
│   ├── 📖 用户文档
│   │   ├── README.md                     # 文档目录说明
│   │   ├── INDEX.md                      # 文档索引
│   │   ├── BEST_PRACTICES.md             # 最佳实践指南 ⭐
│   │   └── API_REFERENCE.md              # API参考文档
│   │
│   ├── ⚙️ 配置文档
│   │   ├── config_reference.md           # 配置参考文档 ⭐
│   │   └── config_fields_explanation.md  # 配置字段说明
│   │
│   ├── 🎯 功能指南
│   │   ├── quick_start_scoring.md        # 评分系统快速入门
│   │   ├── scoring_system_guide.md       # 评分系统详细指南 ⭐
│   │   ├── speed_unit_guide.md           # 速度单位处理指南
│   │   ├── heading_angle_guide.md        # 航向角处理指南
│   │   └── gap_analysis_usage_example.md # 间隙分析使用示例
│   │
│   ├── 🚀 性能优化
│   │   ├── performance_optimization_plan.md # 性能优化计划
│   │   └── performance_optimization_usage_guide.md # 性能优化指南 ⭐
│   │
│   └── 🔧 开发文档
│       └── custom_analyzer_development_guide.md # 自定义分析器开发 ⭐
│
└── 📈 输出结果 (output/)
    ├── test_single/                      # 单文件测试输出
    ├── test_batch_simple/                # 串行批量测试输出
    ├── test_batch_parallel/              # 并行批量测试输出
    ├── test_skip_preprocess/             # 跳过预处理测试输出
    ├── batch_results/                    # 批量处理结果
    ├── performance_optimized/            # 性能优化模式输出
    ├── compatibility_mode/               # 兼容模式输出
    ├── results/                          # 通用结果输出
    └── batch_linux/                      # Linux批量处理输出

=====================================
图例说明:
⭐ = 核心重要文件
📁 = 目录
📋 = 文档文件
🎯 = 主程序
🔧 = 工具/模块
⚙️ = 配置
📊 = 数据
🧪 = 测试
📄 = 模板
📚 = 文档
📈 = 输出

总计文件数: 100+ 个文件
核心模块数: 13 个主要模块
文档数量: 30+ 个文档文件
测试文件数: 8 个测试文件

最后更新: 2025-08-08
版本: v2.0.0
