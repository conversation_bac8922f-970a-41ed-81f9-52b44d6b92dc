#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import io
import csv
import tempfile
from pathlib import Path

from core.preprocessor import RawDataPreprocessor


def _write_lines_to_tempfile(lines):
    fd, path = tempfile.mkstemp(suffix='.txt')
    os.close(fd)
    with open(path, 'w', encoding='utf-8') as f:
        for line in lines:
            f.write(line.rstrip('\n') + '\n')
    return path


def test_speed_unit_conversion_new_json_format():
    """新JSON格式: speed 字段按 km/h 输入, 预处理后应转换为 m/s。"""
    # 构造新格式的一行 JSON（毫秒时间戳 + object_result 列表）
    new_json_line = (
        '{"timestamp": 1730726400000, "object_result": '
        '[{"id": 1, "lat": 39.9, "lon": 116.4, "speed": 36.0, "heading": 90.0}]}'
    )

    input_path = _write_lines_to_tempfile([new_json_line])
    try:
        pre = RawDataPreprocessor()

        # 直接处理JSON为列表数据
        data = pre.process_json_data(input_path)
        assert len(data) == 1
        # 36.0 km/h -> 10 m/s
        assert abs(data[0]['speed'] - 10.0) < 1e-6

        # 通过CSV预处理落盘并验证
        with tempfile.TemporaryDirectory() as tmpdir:
            csv_path = str(Path(tmpdir) / 'perception.csv')
            out_csv = pre.preprocess_perception_file(input_path, csv_path)
            with open(out_csv, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                assert len(rows) == 1
                assert abs(float(rows[0]['speed']) - 10.0) < 1e-6
    finally:
        os.remove(input_path)


def test_speed_unit_conversion_old_json_format():
    """旧JSON格式: PtcSpeed 字段按 km/h 输入, 预处理后应转换为 m/s。"""
    old_json_line = (
        '{"Timestamp": "2025-07-31 12:00:00.000", "Obj_List": '
        '[{"ID": 1, "PtcLat": 39.9, "PtcLon": 116.4, "PtcSpeed": 36.0, "PtcHeading": 90.0}]}'
    )

    input_path = _write_lines_to_tempfile([old_json_line])
    try:
        pre = RawDataPreprocessor()

        data = pre.process_json_data(input_path)
        assert len(data) == 1
        assert abs(data[0]['speed'] - 10.0) < 1e-6

        with tempfile.TemporaryDirectory() as tmpdir:
            csv_path = str(Path(tmpdir) / 'perception.csv')
            out_csv = pre.preprocess_perception_file(input_path, csv_path)
            with open(out_csv, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                rows = list(reader)
                assert len(rows) == 1
                assert abs(float(rows[0]['speed']) - 10.0) < 1e-6
    finally:
        os.remove(input_path)


if __name__ == '__main__':
    ok = True
    try:
        test_speed_unit_conversion_new_json_format()
        print('[OK] 新JSON格式速度单位转换')
    except AssertionError as e:
        ok = False
        print('[FAIL] 新JSON格式速度单位转换:', e)
    try:
        test_speed_unit_conversion_old_json_format()
        print('[OK] 旧JSON格式速度单位转换')
    except AssertionError as e:
        ok = False
        print('[FAIL] 旧JSON格式速度单位转换:', e)
    import sys
    sys.exit(0 if ok else 1)


