## 项目设计文档（Design）

本设计文档描述多目标数据召回/轨迹匹配与精度分析系统的整体架构、核心流程、模块职责、数据模型、可扩展性、性能与测试策略，便于新成员上手与后续演进。

### 1. 目标与范围
- 目标：
  - 将感知系统输出的多目标轨迹与高精度 RTK 参考轨迹进行对齐与匹配。
  - 产出匹配结果、异常分析、精度评估及可视化报告（单任务和批量）。
  - 支持多数据格式输入、统一配置、可插拔分析器与可扩展评分策略。
- 非目标：
  - 不负责实时在线推理；以离线分析为主。
  - 不包含地图匹配或复杂多传感器融合算法实现。

### 2. 输入与输出
- 输入数据：
  - RTK：NMEA（$GPGGA/$GPRMC）、或标准 CSV。
  - 感知：新 JSON（timestamp+object_result）、旧 JSON（Timestamp+Obj_List）、或标准 CSV。
- 输出产物：
  - 匹配 CSV：逐时刻的 RTK/感知对齐记录、误差、分数、异常标签。
  - 诊断 JSON：覆盖率、异常事件统计、质量评估、分段信息等。
  - HTML 报告：单任务精度分析报告、批量汇总报告（Jinja2 渲染）。

### 3. 架构总览

#### 3.1 系统架构图

```mermaid
graph TB
    subgraph "数据输入层"
        A1[RTK轨迹数据<br/>NMEA/CSV/LOG]
        A2[感知数据<br/>JSON/TXT/CSV]
    end

    subgraph "预处理层"
        B[RawDataPreprocessor<br/>格式检测与转换]
        C[CSV标准化<br/>字段统一]
    end

    subgraph "数据加载层"
        D[DataLoader<br/>时间同步与对齐]
        E[GeoUtils<br/>坐标转换]
    end

    subgraph "核心匹配层"
        F[CorridorFilter<br/>走廊过滤]
        G[SimpleDistanceMatcher<br/>轨迹分段与匹配]
        H[UnifiedTrajectoryScorer<br/>统一评分算法]
    end

    subgraph "分析层"
        I[AnomalyAnalysisManager<br/>异常分析管理器]
        J1[SplitDetector<br/>轨迹分裂检测]
        J2[IDSwitchAnalyzer<br/>ID切换分析]
        J3[MissingGapAnalyzer<br/>漏检分析]
        J4[AccuracyAnalyzer<br/>精度分析]
    end

    subgraph "输出生成层"
        K[OutputGenerator<br/>CSV/JSON输出]
        L1[AccuracyReportGenerator<br/>单任务HTML报告]
        L2[BatchReportGenerator<br/>批量汇总报告]
    end

    subgraph "模板渲染层"
        M1[Jinja2模板<br/>accuracy_report_template.html]
        M2[Jinja2模板<br/>batch_summary_template.html]
    end

    A1 --> B
    A2 --> B
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H
    H --> I
    I --> J1
    I --> J2
    I --> J3
    I --> J4
    G --> K
    I --> K
    K --> L1
    K --> L2
    L1 --> M1
    L2 --> M2
```

#### 3.2 数据流向图

```mermaid
flowchart LR
    subgraph "输入"
        RTK[RTK轨迹<br/>31.log]
        PER[感知数据<br/>save_*.txt]
    end

    subgraph "处理"
        PRE[预处理<br/>格式转换]
        MATCH[轨迹匹配<br/>时空对齐]
        SCORE[评分计算<br/>质量评估]
        ANOM[异常检测<br/>问题识别]
    end

    subgraph "输出"
        CSV[匹配结果<br/>*.csv]
        JSON[诊断信息<br/>*.json]
        HTML[分析报告<br/>*.html]
    end

    RTK --> PRE
    PER --> PRE
    PRE --> MATCH
    MATCH --> SCORE
    SCORE --> ANOM
    ANOM --> CSV
    ANOM --> JSON
    ANOM --> HTML
```

#### 3.3 核心入口点

- **单任务处理**: `main.py` → `core/trajectory_matcher.run_matching(...)`
- **串行批量处理**: `batch_simple.py` → 顺序处理多个任务
- **并行批量处理**: `batch_parallel_linux_optimized.py` → 多进程并行处理

#### 3.4 性能优化架构

```mermaid
graph TB
    subgraph "性能优化层"
        P1[RTK查询优化<br/>时间戳预计算+二分查找]
        P2[评分缓存<br/>ScoreCache]
        P3[性能监控<br/>PerformanceMonitor]
        P4[并行处理<br/>多进程Pool]
    end

    subgraph "缓存策略"
        C1[时间对齐缓存<br/>减少重复计算]
        C2[距离计算缓存<br/>Haversine优化]
        C3[评分结果缓存<br/>相同轨迹段复用]
    end

    subgraph "内存优化"
        M1[数据分块加载<br/>大文件处理]
        M2[垃圾回收优化<br/>内存释放]
        M3[对象池复用<br/>减少创建开销]
    end

    P1 --> C1
    P2 --> C3
    P3 --> M2
    P4 --> M1

    C1 --> M3
    C2 --> M3
    C3 --> M3
```

#### 3.5 异常分析架构

```mermaid
graph TB
    subgraph "异常分析管理器"
        AM[AnomalyAnalysisManager<br/>统一调度与汇总]
    end

    subgraph "分析器模块"
        A1[SplitDetector<br/>轨迹分裂检测]
        A2[IDSwitchAnalyzer<br/>ID切换分析]
        A3[MissingGapAnalyzer<br/>漏检间隙分析]
        A4[TimeJumpAnalyzer<br/>时间跳跃分析]
        A5[TrajectoryQualityAnalyzer<br/>轨迹质量分析]
        A6[AccuracyAnalyzer<br/>精度分析]
    end

    subgraph "结果处理"
        RS[ResultSummarizer<br/>结果汇总器]
        EL[事件列表<br/>时间戳+严重度+详情]
        SS[统计摘要<br/>异常计数+质量指标]
    end

    AM --> A1
    AM --> A2
    AM --> A3
    AM --> A4
    AM --> A5
    AM --> A6

    A1 --> RS
    A2 --> RS
    A3 --> RS
    A4 --> RS
    A5 --> RS
    A6 --> RS

    RS --> EL
    RS --> SS
```

#### 3.6 配置管理架构

```mermaid
graph TB
    subgraph "配置文件"
        UC[unified_config.json<br/>统一配置文件]
        DC[default.json<br/>默认配置]
        BC[batch_config.json<br/>批量配置]
    end

    subgraph "配置加载器"
        CL[ConfigLoader<br/>配置加载与验证]
        FD[格式检测<br/>嵌套/扁平自动识别]
        LC[LegacyConfig<br/>向后兼容映射]
    end

    subgraph "配置域"
        ROI[ROI/走廊过滤<br/>roi.*, corridor.*]
        MATCH[匹配参数<br/>matching.*]
        SCORE[评分配置<br/>scoring.*]
        ANOM[异常检测<br/>anomaly.*]
        PROF[配置档案<br/>profiles.*]
    end

    UC --> CL
    DC --> CL
    BC --> CL

    CL --> FD
    CL --> LC

    FD --> ROI
    FD --> MATCH
    FD --> SCORE
    FD --> ANOM
    FD --> PROF
```

#### 3.7 部署与运行架构

```mermaid
graph TB
    subgraph "运行环境"
        ENV1[Windows 10/11<br/>开发与测试环境]
        ENV2[Linux Server<br/>生产与并行处理]
        ENV3[Python 3.9+<br/>运行时环境]
    end

    subgraph "执行模式"
        MODE1[单任务模式<br/>main.py]
        MODE2[串行批量模式<br/>batch_simple.py]
        MODE3[并行批量模式<br/>batch_parallel_linux_optimized.py]
    end

    subgraph "输入数据"
        DATA1[本地文件<br/>RTK + 感知数据]
        DATA2[批量配置<br/>batch.csv]
        DATA3[系统配置<br/>unified_config.json]
    end

    subgraph "输出产物"
        OUT1[匹配结果<br/>CSV文件]
        OUT2[诊断信息<br/>JSON文件]
        OUT3[分析报告<br/>HTML文件]
        OUT4[批量汇总<br/>HTML报告]
    end

    ENV1 --> MODE1
    ENV2 --> MODE2
    ENV2 --> MODE3
    ENV3 --> MODE1
    ENV3 --> MODE2
    ENV3 --> MODE3

    DATA1 --> MODE1
    DATA1 --> MODE2
    DATA1 --> MODE3
    DATA2 --> MODE2
    DATA2 --> MODE3
    DATA3 --> MODE1
    DATA3 --> MODE2
    DATA3 --> MODE3

    MODE1 --> OUT1
    MODE1 --> OUT2
    MODE1 --> OUT3
    MODE2 --> OUT4
    MODE3 --> OUT4
```

#### 3.8 测试架构

```mermaid
graph TB
    subgraph "测试层次"
        T1[单元测试<br/>核心功能验证]
        T2[集成测试<br/>端到端流程]
        T3[性能测试<br/>基准与回归]
        T4[回归测试<br/>功能稳定性]
    end

    subgraph "测试模块"
        UT1[速度单位转换测试<br/>test_speed_unit_conversion.py]
        UT2[统一评分回归测试<br/>test_unified_scoring_regression.py]
        IT1[精度报告集成测试<br/>test_accuracy_report_integration.py]
        PT1[性能基准测试<br/>performance_benchmark_test.py]
        PT2[真实数据性能测试<br/>real_data_performance_test.py]
    end

    subgraph "测试数据"
        TD1[标准测试数据集<br/>data/31.log + save_*.txt]
        TD2[边界测试数据<br/>异常场景数据]
        TD3[性能测试数据<br/>大规模数据集]
    end

    subgraph "测试结果"
        TR1[测试报告<br/>通过/失败状态]
        TR2[性能指标<br/>执行时间与资源使用]
        TR3[覆盖率报告<br/>代码覆盖情况]
    end

    T1 --> UT1
    T1 --> UT2
    T2 --> IT1
    T3 --> PT1
    T3 --> PT2
    T4 --> UT1
    T4 --> UT2
    T4 --> IT1

    UT1 --> TD1
    UT2 --> TD1
    IT1 --> TD1
    PT1 --> TD3
    PT2 --> TD3

    UT1 --> TR1
    UT2 --> TR1
    IT1 --> TR1
    PT1 --> TR2
    PT2 --> TR2
    IT1 --> TR3
```

### 4. 架构设计原则

#### 4.1 设计理念
- **模块化设计**: 每个模块职责单一，接口清晰，便于测试和维护
- **可扩展性**: 支持新增分析器、评分策略、数据格式等
- **性能优化**: 内置缓存机制、并行处理、算法优化
- **向后兼容**: 保持API稳定性，支持配置文件向后兼容
- **错误处理**: 完善的异常处理和错误恢复机制

#### 4.2 架构约束
- **数据流向**: 单向数据流，避免循环依赖
- **配置驱动**: 核心参数通过配置文件管理，支持运行时调整
- **接口标准**: 统一的数据模型和接口规范
- **资源管理**: 合理的内存使用和资源释放
- **日志规范**: 统一的日志格式和级别管理

#### 4.3 质量保证
- **测试覆盖**: 单元测试、集成测试、性能测试全覆盖
- **代码规范**: 统一的代码风格和命名规范
- **文档完善**: 完整的API文档和使用指南
- **版本管理**: 清晰的版本控制和变更日志
- **持续集成**: 自动化测试和质量检查

### 5. 关键模块设计

#### 5.1 配置与参数（`core/config_loader.py`）
- 支持嵌套与扁平两种 JSON 格式；`ConfigLoader.load_config` 自动检测/转换。
- `profiles`（如 `simple_distance`、`performance_optimized`、`compatibility_mode`）支持按场景覆盖参数。
- `LegacyConfig` 对原有扁平属性做映射，保障向后兼容。

主要参数域：
- ROI/走廊过滤（`roi`/`corridor`）
- 匹配（`matching`）：时间窗、阈值、最小段长、缓冲等
- 评分（`scoring`）：统一评分/F1/传统策略的权重与阈值
- 异常检测（`anomaly`）：切换、漏检、时间跳变阈值

#### 5.2 预处理（`core/preprocessor.py`）
- 自动检测文件格式（CSV/NMEA/JSON）。
- NMEA：解析 $GPGGA/$GPRMC，统一输出 `timestamp/lat/lon/speed/heading`。
- JSON：兼容新/旧结构；速度按 km/h → m/s 标准化；时间戳标准化为北京时间字符串。
- CSV：根据列名别名做轻量重命名与列筛选。

设计要点：
- 统一字段命名，保证后续流水线数据契约稳定。
- 明确单位/时区转换（速度 m/s，时间默认北京时区），可扩展为配置化。

#### 5.3 数据模型与工具（`core/data_utils.py`）
- 数据类：`RTKPoint`、`PerceptionPoint`。
- `DataLoader`：CSV 加载、时间同步（RTK UTC→北京；感知认为已在北京时区）。
- `GeoUtils`：Haversine 距离、航向计算、WGS84→UTM。
- `TimeSync`：时间解析与双向转换。

#### 5.4 匹配与评分（`core/simple_distance_matcher.py`）
- 过滤：
  - 走廊过滤（优先）或 ROI 过滤（回退）。
  - 基于 RTK 轨迹构造走廊，按空间/时间阈值筛选感知点。
- 分段：按 `id` 聚合、按时间排序、构建 `TrajectorySegment`。
- 统一评分（`UnifiedTrajectoryScorer`）：
  - 兼顾短/长轨迹的策略组合：短轨优先整体质量、长轨峰值优先或分段优先、其余回退到改进传统策略；质量由距离指数衰减转为分数。
  - 性能优化：时间对齐搜索（预计算时间戳数组/弧度坐标）、评分缓存（`ScoreCache`）、性能监控（`PerformanceMonitor`）。
- 核心链构建：
  - 预裁剪低质量段；在候选集合中竞争选优；删除被覆盖段。

#### 5.5 异常分析（`core/anomaly_analysis/*`）
- 管理器：`AnomalyAnalysisManager` 统一调度各分析器并汇总（`result_summarizer.py`）。
- 分析器：分裂（`split_detector.py`）、ID 切换、漏检间隙、时间跳变、轨迹质量等。
- 输出：事件列表（时间戳、相关 ID、严重度、细节）与统计摘要。

#### 5.6 输出与报告
- `OutputGenerator`：
  - 匹配 CSV：RTK/感知对、误差、分数、异常标签。
  - 诊断 JSON：元数据（覆盖率、时长等）、异常统计、分段信息、（可选）精度分析结果。
- 精度报告（`core/report_generator.py`）：
  - 输入：`AccuracyAnalyzer` 产出；渲染：Jinja2（`templates/accuracy_report_template.html`）。
  - 包含总体质量、定位/速度/航向指标、时间序列、分布、时间窗口分析、异常事件与建议。
- 批量汇总（`core/batch_report_generator.py`）：
  - 输入：批量运行摘要；渲染：Jinja2（`templates/batch_summary_template.html`）。

### 6. 端到端流程（单任务）
1) 预处理（可跳过）：原始文件 → 标准 CSV。
2) 加载与对齐：解析 CSV → `RTKPoint/PerceptionPoint`，时间同步。
3) 过滤与分段：走廊/ROI → 分段（按 id）。
4) 匹配与评分：统一评分/缓存/优化 → 核心链。
5) 异常分析：分裂/切换/间隙/跳变等。
6) 产出：匹配 CSV、诊断 JSON、精度 HTML 报告。

CLI 示例：
```bash
python main.py \
  --perception data/save_1753355915725.txt \
  --rtk data/31.log \
  --output-dir output/single_analysis \
  --skip-preprocess
```

### 7. 配置约定
- 统一配置文件：`config/unified_config.json`（支持 `profiles`）。
- 常用字段：
  - `corridor.enabled`、`corridor.long_buffer_meters/lat_buffer_meters`、`corridor.time_buffer_seconds`。
  - `matching.win_sec/local_match_thr/min_segment_length/rtk_buffer`。
  - `scoring.peak_weight/duration_weight/stability_weight`；统一评分自带内部阈值（短轨阈值、峰值窗口等）。
  - `anomaly.switch_dt/dist/speed/heading`、`min_missing_gap/max_missing_gap`。

### 8. 扩展性设计
- 新增分析器：继承 `BaseAnomalyAnalyzer`，实现 `analyze` 与结果结构，注册至管理器。
- 自定义评分策略：
  - 在统一评分内新增策略分支，或扩展 `scoring.method` 为可选值（如 `f1_style`）。
  - 通过配置参数暴露阈值与窗口长度。
- 模板扩展：新增 Jinja2 模板文件，或调整现有模板变量与块；保持 `AccuracyReportGenerator`/`BatchReportGenerator` 的接口不变。
- 数据格式扩展：在预处理器中增强 `detect_file_format` 与解析逻辑，统一输出字段即可融入流水线。

### 9. 性能与并行
- 匹配阶段：时间对齐搜索（二分）、弧度坐标预计算、评分缓存，降低 O(N×M) 查询成本。
- 批量处理：`batch_parallel_linux_optimized.py` 多进程并行，产出批量 HTML 总结报告。
- 日志监控：`PerformanceMonitor` 汇总 RTK 查询统计；`ScoreCache` 输出缓存命中率。

### 10. 日志、错误与健壮性
- 日志：`logging` 贯穿预处理/匹配/分析/渲染；`--verbose` 可增强调试信息。
- 错误处理：
  - 文件/格式校验、缺失字段检查与回退路径（如走廊失败回退 ROI）。
  - 配置加载失败时回退默认参数（并告警）。
  - 生成报告/写盘异常捕获并提示。

### 11. 测试策略
- 单元/回归：
  - 速度单位转换：`tests/test_speed_unit_conversion.py`（新/旧 JSON km/h→m/s）。
  - 统一评分回归：`tests/test_unified_scoring_regression.py`（短/长轨策略与分值门限）。
- 集成：
  - 精度报告集成：`tests/test_accuracy_report_integration.py`（CSV/JSON/HTML 产出完整性、Jinja2 渲染）。

运行：
```bash
python -m tests.test_speed_unit_conversion
python -m tests.test_unified_scoring_regression
python tests/test_accuracy_report_integration.py
```

### 12. 部署与运行
- 依赖：`requirements.txt`（已加入 `Jinja2>=3.1.0`）。
- Python：建议 3.9+。
- 平台：Windows/Linux；并行批处理推荐 Linux 版本脚本。

### 13. 风险与边界
- 时区：RTK 读取时假定 UTC，感知时间按北京时区；跨时区数据需在预处理层显式转换。
- 单位：速度统一为 m/s；第三方数据源的单位需在预处理/配置中对齐。
- 稀疏/短轨：短轨策略避免被长轨评分拖累，但极端稀疏数据仍需门限保护。

### 14. 路线图（Roadmap）
- 参数化：将时区/速度单位纳入 `processing.*` 配置并在日志中记录转换来源。
- 评分：统一评分参数外部化，并加入更多稳定性/连续性指标；引入可学习权重。
- 并行：跨平台并行实现与资源自适应调度；批量进度可视化。
- 可视化：报告交互增强、图表懒加载、轻量数据下钻。
- 工程化：CI（lint/测试）、打包与发布、CLI 文档自动生成。

### 15. 目录结构（详细）

#### 15.1 核心代码模块
```
core/
├── __init__.py                        # 模块初始化
├── preprocessor.py                    # 数据预处理与格式转换
├── data_utils.py                      # 数据模型/工具/加载/时间同步
├── corridor_filter.py                # 走廊过滤算法
├── simple_distance_matcher.py        # 轨迹分段/匹配/评分/核心链
├── trajectory_matcher.py             # 端到端匹配主流程（run_matching）
├── config_loader.py                  # 配置加载与管理
├── output_generator.py               # CSV/JSON输出生成
├── report_generator.py               # 单任务精度HTML报告生成
├── batch_report_generator.py         # 批量汇总HTML报告生成
├── gap_analyzer.py                   # 间隙分析器
└── anomaly_analysis/                 # 异常分析框架
    ├── __init__.py                   # 异常分析模块初始化
    ├── analysis_manager.py           # 异常分析管理器
    ├── base_analyzer.py              # 分析器基类
    ├── result_summarizer.py          # 结果汇总器
    ├── accuracy_analyzer.py          # 精度分析器
    ├── id_switch_analyzer.py         # ID切换分析器
    ├── missing_gap_analyzer.py       # 漏检间隙分析器
    ├── split_detector.py             # 轨迹分裂检测器
    ├── time_jump_analyzer.py         # 时间跳跃分析器
    └── trajectory_quality_analyzer.py # 轨迹质量分析器
```

#### 15.2 配置与模板
```
config/
├── unified_config.json               # 统一配置文件（支持profiles）
├── default.json                      # 默认配置文件
└── batch_config.json                 # 批量处理配置文件

templates/
├── accuracy_report_template.html     # 精度分析报告Jinja2模板
└── batch_summary_template.html       # 批量汇总报告Jinja2模板
```

#### 15.3 测试体系
```
tests/
├── test_speed_unit_conversion.py     # 速度单位转换测试
├── test_unified_scoring_regression.py # 统一评分回归测试
├── test_accuracy_report_integration.py # 精度报告集成测试
├── test_performance_optimization.py   # 性能优化测试
├── performance_benchmark_test.py      # 性能基准测试
├── real_data_performance_test.py      # 真实数据性能测试
├── accuracy_comparison_test.py        # 精度对比测试
└── parameter_tuning.py                # 参数调优测试
```

#### 15.4 工具与文档
```
tools/
└── check_config.py                   # 配置文件检查工具

docs/
├── API_REFERENCE.md                  # API参考文档
├── BEST_PRACTICES.md                 # 最佳实践指南
├── config_reference.md               # 配置参考文档
├── scoring_system_guide.md           # 评分系统指南
├── performance_optimization_usage_guide.md # 性能优化指南
└── custom_analyzer_development_guide.md # 自定义分析器开发指南
```

### 16. CLI 约定（统一）
- 单任务：
  - `--rtk PATH`、`--perception PATH`、`--config PATH`、`--profile {simple_distance|performance_optimized|compatibility_mode}`
  - `--output-dir PATH`、`--skip-preprocess`、`--verbose`
- 批量：
  - `--batch CSV`、`--config PATH`、并行脚本支持 `--workers`、`--output`。

以上设计为当前实现的抽象与约束，后续演进将保持向后兼容为优先，重大变更会在 `CHANGELOG.md` 与文档中同步。


