## 项目设计文档（Design）

本设计文档描述多目标数据召回/轨迹匹配与精度分析系统的整体架构、核心流程、模块职责、数据模型、可扩展性、性能与测试策略，便于新成员上手与后续演进。

### 1. 目标与范围
- 目标：
  - 将感知系统输出的多目标轨迹与高精度 RTK 参考轨迹进行对齐与匹配。
  - 产出匹配结果、异常分析、精度评估及可视化报告（单任务和批量）。
  - 支持多数据格式输入、统一配置、可插拔分析器与可扩展评分策略。
- 非目标：
  - 不负责实时在线推理；以离线分析为主。
  - 不包含地图匹配或复杂多传感器融合算法实现。

### 2. 输入与输出
- 输入数据：
  - RTK：NMEA（$GPGGA/$GPRMC）、或标准 CSV。
  - 感知：新 JSON（timestamp+object_result）、旧 JSON（Timestamp+Obj_List）、或标准 CSV。
- 输出产物：
  - 匹配 CSV：逐时刻的 RTK/感知对齐记录、误差、分数、异常标签。
  - 诊断 JSON：覆盖率、异常事件统计、质量评估、分段信息等。
  - HTML 报告：单任务精度分析报告、批量汇总报告（Jinja2 渲染）。

### 3. 架构总览

```mermaid
graph LR
  A[原始数据 RTK/感知] --> B[预处理 RawDataPreprocessor]
  B --> C[CSV 标准化]
  C --> D[DataLoader 加载 + 时间同步]
  D --> E[SimpleDistanceMatcher 过滤/分段/匹配]
  E --> F[异常分析 AnomalyAnalysisManager]
  E --> G[评分(统一/传统/F1) + 核心链]
  F --> H[OutputGenerator CSV/JSON]
  G --> H
  H --> I[AccuracyReportGenerator Jinja2 HTML]
  H --> J[BatchReportGenerator Jinja2 HTML]
```

核心入口：`main.py` → `core/trajectory_matcher.run_matching(...)`；批量入口：`batch_simple.py`、`batch_parallel_linux_optimized.py`。

### 4. 关键模块设计

#### 4.1 配置与参数（`core/config_loader.py`）
- 支持嵌套与扁平两种 JSON 格式；`ConfigLoader.load_config` 自动检测/转换。
- `profiles`（如 `simple_distance`、`performance_optimized`、`compatibility_mode`）支持按场景覆盖参数。
- `LegacyConfig` 对原有扁平属性做映射，保障向后兼容。

主要参数域：
- ROI/走廊过滤（`roi`/`corridor`）
- 匹配（`matching`）：时间窗、阈值、最小段长、缓冲等
- 评分（`scoring`）：统一评分/F1/传统策略的权重与阈值
- 异常检测（`anomaly`）：切换、漏检、时间跳变阈值

#### 4.2 预处理（`core/preprocessor.py`）
- 自动检测文件格式（CSV/NMEA/JSON）。
- NMEA：解析 $GPGGA/$GPRMC，统一输出 `timestamp/lat/lon/speed/heading`。
- JSON：兼容新/旧结构；速度按 km/h → m/s 标准化；时间戳标准化为北京时间字符串。
- CSV：根据列名别名做轻量重命名与列筛选。

设计要点：
- 统一字段命名，保证后续流水线数据契约稳定。
- 明确单位/时区转换（速度 m/s，时间默认北京时区），可扩展为配置化。

#### 4.3 数据模型与工具（`core/data_utils.py`）
- 数据类：`RTKPoint`、`PerceptionPoint`。
- `DataLoader`：CSV 加载、时间同步（RTK UTC→北京；感知认为已在北京时区）。
- `GeoUtils`：Haversine 距离、航向计算、WGS84→UTM。
- `TimeSync`：时间解析与双向转换。

#### 4.4 匹配与评分（`core/simple_distance_matcher.py`）
- 过滤：
  - 走廊过滤（优先）或 ROI 过滤（回退）。
  - 基于 RTK 轨迹构造走廊，按空间/时间阈值筛选感知点。
- 分段：按 `id` 聚合、按时间排序、构建 `TrajectorySegment`。
- 统一评分（`UnifiedTrajectoryScorer`）：
  - 兼顾短/长轨迹的策略组合：短轨优先整体质量、长轨峰值优先或分段优先、其余回退到改进传统策略；质量由距离指数衰减转为分数。
  - 性能优化：时间对齐搜索（预计算时间戳数组/弧度坐标）、评分缓存（`ScoreCache`）、性能监控（`PerformanceMonitor`）。
- 核心链构建：
  - 预裁剪低质量段；在候选集合中竞争选优；删除被覆盖段。

#### 4.5 异常分析（`core/anomaly_analysis/*`）
- 管理器：`AnomalyAnalysisManager` 统一调度各分析器并汇总（`result_summarizer.py`）。
- 分析器：分裂（`split_detector.py`）、ID 切换、漏检间隙、时间跳变、轨迹质量等。
- 输出：事件列表（时间戳、相关 ID、严重度、细节）与统计摘要。

#### 4.6 输出与报告
- `OutputGenerator`：
  - 匹配 CSV：RTK/感知对、误差、分数、异常标签。
  - 诊断 JSON：元数据（覆盖率、时长等）、异常统计、分段信息、（可选）精度分析结果。
- 精度报告（`core/report_generator.py`）：
  - 输入：`AccuracyAnalyzer` 产出；渲染：Jinja2（`templates/accuracy_report_template.html`）。
  - 包含总体质量、定位/速度/航向指标、时间序列、分布、时间窗口分析、异常事件与建议。
- 批量汇总（`core/batch_report_generator.py`）：
  - 输入：批量运行摘要；渲染：Jinja2（`templates/batch_summary_template.html`）。

### 5. 端到端流程（单任务）
1) 预处理（可跳过）：原始文件 → 标准 CSV。
2) 加载与对齐：解析 CSV → `RTKPoint/PerceptionPoint`，时间同步。
3) 过滤与分段：走廊/ROI → 分段（按 id）。
4) 匹配与评分：统一评分/缓存/优化 → 核心链。
5) 异常分析：分裂/切换/间隙/跳变等。
6) 产出：匹配 CSV、诊断 JSON、精度 HTML 报告。

CLI 示例：
```bash
python main.py \
  --perception data/save_1753355915725.txt \
  --rtk data/31.log \
  --output-dir output/single_analysis \
  --skip-preprocess
```

### 6. 配置约定
- 统一配置文件：`config/unified_config.json`（支持 `profiles`）。
- 常用字段：
  - `corridor.enabled`、`corridor.long_buffer_meters/lat_buffer_meters`、`corridor.time_buffer_seconds`。
  - `matching.win_sec/local_match_thr/min_segment_length/rtk_buffer`。
  - `scoring.peak_weight/duration_weight/stability_weight`；统一评分自带内部阈值（短轨阈值、峰值窗口等）。
  - `anomaly.switch_dt/dist/speed/heading`、`min_missing_gap/max_missing_gap`。

### 7. 扩展性设计
- 新增分析器：继承 `BaseAnomalyAnalyzer`，实现 `analyze` 与结果结构，注册至管理器。
- 自定义评分策略：
  - 在统一评分内新增策略分支，或扩展 `scoring.method` 为可选值（如 `f1_style`）。
  - 通过配置参数暴露阈值与窗口长度。
- 模板扩展：新增 Jinja2 模板文件，或调整现有模板变量与块；保持 `AccuracyReportGenerator`/`BatchReportGenerator` 的接口不变。
- 数据格式扩展：在预处理器中增强 `detect_file_format` 与解析逻辑，统一输出字段即可融入流水线。

### 8. 性能与并行
- 匹配阶段：时间对齐搜索（二分）、弧度坐标预计算、评分缓存，降低 O(N×M) 查询成本。
- 批量处理：`batch_parallel_linux_optimized.py` 多进程并行，产出批量 HTML 总结报告。
- 日志监控：`PerformanceMonitor` 汇总 RTK 查询统计；`ScoreCache` 输出缓存命中率。

### 9. 日志、错误与健壮性
- 日志：`logging` 贯穿预处理/匹配/分析/渲染；`--verbose` 可增强调试信息。
- 错误处理：
  - 文件/格式校验、缺失字段检查与回退路径（如走廊失败回退 ROI）。
  - 配置加载失败时回退默认参数（并告警）。
  - 生成报告/写盘异常捕获并提示。

### 10. 测试策略
- 单元/回归：
  - 速度单位转换：`tests/test_speed_unit_conversion.py`（新/旧 JSON km/h→m/s）。
  - 统一评分回归：`tests/test_unified_scoring_regression.py`（短/长轨策略与分值门限）。
- 集成：
  - 精度报告集成：`tests/test_accuracy_report_integration.py`（CSV/JSON/HTML 产出完整性、Jinja2 渲染）。

运行：
```bash
python -m tests.test_speed_unit_conversion
python -m tests.test_unified_scoring_regression
python tests/test_accuracy_report_integration.py
```

### 11. 部署与运行
- 依赖：`requirements.txt`（已加入 `Jinja2>=3.1.0`）。
- Python：建议 3.9+。
- 平台：Windows/Linux；并行批处理推荐 Linux 版本脚本。

### 12. 风险与边界
- 时区：RTK 读取时假定 UTC，感知时间按北京时区；跨时区数据需在预处理层显式转换。
- 单位：速度统一为 m/s；第三方数据源的单位需在预处理/配置中对齐。
- 稀疏/短轨：短轨策略避免被长轨评分拖累，但极端稀疏数据仍需门限保护。

### 13. 路线图（Roadmap）
- 参数化：将时区/速度单位纳入 `processing.*` 配置并在日志中记录转换来源。
- 评分：统一评分参数外部化，并加入更多稳定性/连续性指标；引入可学习权重。
- 并行：跨平台并行实现与资源自适应调度；批量进度可视化。
- 可视化：报告交互增强、图表懒加载、轻量数据下钻。
- 工程化：CI（lint/测试）、打包与发布、CLI 文档自动生成。

### 14. 目录结构（摘录）
```
core/
  preprocessor.py            # 预处理
  data_utils.py             # 数据模型/工具/加载/时间同步
  simple_distance_matcher.py# 过滤/分段/评分/核心链/优化
  trajectory_matcher.py     # 端到端匹配与产出主流程（run_matching）
  output_generator.py       # CSV/JSON 生成
  report_generator.py       # 单任务精度 Jinja2 报告
  batch_report_generator.py # 批量汇总 Jinja2 报告
  anomaly_analysis/         # 异常分析框架与分析器
templates/
  accuracy_report_template.html
  batch_summary_template.html
config/
  unified_config.json       # 统一配置（支持 profiles）
tests/
  test_speed_unit_conversion.py
  test_unified_scoring_regression.py
  test_accuracy_report_integration.py
```

### 15. CLI 约定（统一）
- 单任务：
  - `--rtk PATH`、`--perception PATH`、`--config PATH`、`--profile {simple_distance|performance_optimized|compatibility_mode}`
  - `--output-dir PATH`、`--skip-preprocess`、`--verbose`
- 批量：
  - `--batch CSV`、`--config PATH`、并行脚本支持 `--workers`、`--output`。

以上设计为当前实现的抽象与约束，后续演进将保持向后兼容为优先，重大变更会在 `CHANGELOG.md` 与文档中同步。


