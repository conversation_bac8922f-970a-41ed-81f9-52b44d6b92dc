# 📋 车路协同轨迹匹配系统 - 文件清单

## 🎯 核心程序文件 (3个)

| 文件名 | 功能描述 | 重要性 | 状态 |
|--------|----------|--------|------|
| `main.py` | 单文件轨迹匹配处理主程序 | ⭐⭐⭐ | 生产就绪 |
| `batch_simple.py` | 串行批量处理程序 | ⭐⭐⭐ | 生产就绪 |
| `batch_parallel_linux_optimized.py` | 并行批量处理程序（Linux优化） | ⭐⭐⭐ | 生产就绪 |

## 🔧 核心模块文件 (12个)

### 主要算法模块
| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `core/trajectory_matcher.py` | 轨迹匹配核心算法 | ⭐⭐⭐ |
| `core/preprocessor.py` | 数据预处理模块 | ⭐⭐⭐ |
| `core/corridor_filter.py` | 走廊过滤算法 | ⭐⭐⭐ |
| `core/simple_distance_matcher.py` | 简单距离匹配器 | ⭐⭐ |

### 数据处理模块
| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `core/data_utils.py` | 数据工具函数 | ⭐⭐⭐ |
| `core/gap_analyzer.py` | 间隙分析器 | ⭐⭐ |
| `core/config_loader.py` | 配置加载器 | ⭐⭐⭐ |

### 输出生成模块
| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `core/output_generator.py` | 输出生成器 | ⭐⭐ |
| `core/report_generator.py` | 单文件报告生成器 | ⭐⭐⭐ |
| `core/batch_report_generator.py` | 批量报告生成器 | ⭐⭐⭐ |

## 🔍 异常分析模块 (9个)

| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `core/anomaly_analysis/analysis_manager.py` | 异常分析管理器 | ⭐⭐⭐ |
| `core/anomaly_analysis/base_analyzer.py` | 分析器基类 | ⭐⭐⭐ |
| `core/anomaly_analysis/result_summarizer.py` | 结果汇总器 | ⭐⭐ |
| `core/anomaly_analysis/accuracy_analyzer.py` | 精度分析器 | ⭐⭐⭐ |
| `core/anomaly_analysis/id_switch_analyzer.py` | ID切换分析器 | ⭐⭐ |
| `core/anomaly_analysis/missing_gap_analyzer.py` | 漏检间隙分析器 | ⭐⭐ |
| `core/anomaly_analysis/split_detector.py` | 轨迹分裂检测器 | ⭐⭐ |
| `core/anomaly_analysis/time_jump_analyzer.py` | 时间跳跃分析器 | ⭐⭐ |
| `core/anomaly_analysis/trajectory_quality_analyzer.py` | 轨迹质量分析器 | ⭐⭐ |

## ⚙️ 配置文件 (3个)

| 文件名 | 功能描述 | 重要性 | 状态 |
|--------|----------|--------|------|
| `config/unified_config.json` | 统一配置文件（主要使用） | ⭐⭐⭐ | 活跃 |
| `config/default.json` | 默认配置文件 | ⭐⭐ | 备用 |
| `config/batch_config.json` | 批量处理配置文件 | ⭐⭐ | 备用 |

## 📊 数据文件 (30+个)

### RTK轨迹数据 (15个)
```
data/31.log ~ data/36.log                    # RTK轨迹数据文件 (6个)
data/rtk_part001.txt ~ data/rtk_part009.txt  # RTK分段数据文件 (9个)
```

### 感知数据 (15+个)
```
data/save_1753355915725.txt                  # 主要感知数据样本
data/AJ06993PAJ00*.txt                       # 感知数据集合 (8个)
data/ST101211100*.txt                        # 感知数据集合 (4个)
data/first.txt, data/second.txt              # 测试数据 (2个)
```

### 配置数据 (1个)
```
data/batch.csv                               # 批量处理任务配置文件
```

## 🧪 测试文件 (8个)

### 单元测试 (4个)
| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `tests/test_speed_unit_conversion.py` | 速度单位转换测试 | ⭐⭐⭐ |
| `tests/test_unified_scoring_regression.py` | 统一评分回归测试 | ⭐⭐⭐ |
| `tests/test_accuracy_report_integration.py` | 精度报告集成测试 | ⭐⭐⭐ |
| `tests/test_performance_optimization.py` | 性能优化测试 | ⭐⭐ |

### 性能测试 (4个)
| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `tests/performance_benchmark_test.py` | 性能基准测试 | ⭐⭐ |
| `tests/real_data_performance_test.py` | 真实数据性能测试 | ⭐⭐ |
| `tests/accuracy_comparison_test.py` | 精度对比测试 | ⭐⭐ |
| `tests/parameter_tuning.py` | 参数调优测试 | ⭐ |

## 📄 模板文件 (2个)

| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `templates/accuracy_report_template.html` | 精度分析报告模板 | ⭐⭐⭐ |
| `templates/batch_summary_template.html` | 批量汇总报告模板 | ⭐⭐⭐ |

## 📚 文档文件 (30+个)

### 主要文档 (8个)
| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `README.md` | 项目主要说明文档 | ⭐⭐⭐ |
| `CHANGELOG.md` | 版本更新日志 | ⭐⭐⭐ |
| `DOCUMENTATION.md` | 完整文档索引 | ⭐⭐ |
| `QUICK_START.md` | 快速开始指南 | ⭐⭐⭐ |
| `BATCH_PROCESSING_GUIDE.md` | 批量处理指南 | ⭐⭐⭐ |
| `PROJECT_STRUCTURE.md` | 项目结构说明 | ⭐⭐ |
| `DIRECTORY_TREE.txt` | 目录树结构 | ⭐⭐ |
| `requirements.txt` | Python依赖包列表 | ⭐⭐⭐ |

### 中文文档 (4个)
| 文件名 | 功能描述 | 重要性 |
|--------|----------|--------|
| `新手快速上手指南.md` | 中文快速入门指南 | ⭐⭐⭐ |
| `文档使用指南.md` | 中文文档使用指南 | ⭐⭐ |
| `批量处理HTML报告使用指南.md` | 中文报告使用指南 | ⭐⭐ |
| `项目完成总结.md` | 项目总结文档 | ⭐⭐ |

### 详细文档 (14个)
```
docs/README.md                              # 文档目录说明
docs/INDEX.md                               # 文档索引
docs/BEST_PRACTICES.md                      # 最佳实践指南 ⭐⭐⭐
docs/API_REFERENCE.md                       # API参考文档 ⭐⭐
docs/config_reference.md                    # 配置参考文档 ⭐⭐⭐
docs/config_fields_explanation.md           # 配置字段说明 ⭐⭐
docs/scoring_system_guide.md                # 评分系统详细指南 ⭐⭐⭐
docs/performance_optimization_usage_guide.md # 性能优化使用指南 ⭐⭐⭐
docs/custom_analyzer_development_guide.md   # 自定义分析器开发指南 ⭐⭐
docs/quick_start_scoring.md                 # 评分系统快速入门 ⭐⭐
docs/speed_unit_guide.md                    # 速度单位处理指南 ⭐⭐
docs/heading_angle_guide.md                 # 航向角处理指南 ⭐⭐
docs/gap_analysis_usage_example.md          # 间隙分析使用示例 ⭐⭐
docs/performance_optimization_plan.md       # 性能优化计划 ⭐
```

## 🔧 工具脚本 (1个)

| 文件名 | 功能描述 | 重要性 | 状态 |
|--------|----------|--------|------|
| `tools/check_config.py` | 配置文件检查工具 | ⭐⭐ | 活跃 |

## 📈 输出目录 (9个)

```
output/test_single/                         # 单文件测试输出
output/test_batch_simple/                   # 串行批量测试输出
output/test_batch_parallel/                 # 并行批量测试输出
output/test_skip_preprocess/                # 跳过预处理测试输出
output/batch_results/                       # 批量处理结果
output/performance_optimized/               # 性能优化模式输出
output/compatibility_mode/                  # 兼容模式输出
output/results/                             # 通用结果输出
output/batch_linux/                         # Linux批量处理输出
```

## 📊 统计汇总

| 类别 | 数量 | 核心文件数 |
|------|------|-----------|
| 🎯 主程序 | 3 | 3 |
| 🔧 核心模块 | 21 | 12 |
| ⚙️ 配置文件 | 3 | 1 |
| 📊 数据文件 | 30+ | 2 |
| 🧪 测试文件 | 8 | 4 |
| 📄 模板文件 | 2 | 2 |
| 📚 文档文件 | 30+ | 8 |
| 🔧 工具脚本 | 1 | 1 |
| **总计** | **90+** | **33** |

## 🏷️ 重要性标记说明

- ⭐⭐⭐ = 核心关键文件，系统运行必需
- ⭐⭐ = 重要文件，影响系统功能
- ⭐ = 辅助文件，可选或备用

---

📅 **最后更新**: 2025-08-08  
🔖 **版本**: v2.0.0  
📋 **文件总数**: 100+ 个文件  
🎯 **核心文件**: 35 个关键文件
