#!/usr/bin/env python3
# -*- coding: utf-8 -*-

from datetime import datetime, timedelta

from core.simple_distance_matcher import UnifiedTrajectoryScorer


class _Point:
    def __init__(self, t, lat, lon, speed=10.0, heading=90.0):
        self.timestamp = t
        self.lat = lat
        self.lon = lon
        self.speed = speed
        self.heading = heading


class _Seg:
    def __init__(self, id_, points):
        self.id = id_
        self.points = points
        self.start_time = points[0].timestamp
        self.end_time = points[-1].timestamp
        self.duration = (self.end_time - self.start_time).total_seconds()


def _build_rtk_and_per(duration_s=5.0, sampling_hz=10.0, offset_m=0.0):
    # 生成直线轨迹，按秒移动约11m（粗略），偏移 offset_m 米来控制质量
    num = int(duration_s * sampling_hz)
    base_lat, base_lon = 39.0, 116.0
    start = datetime(2025, 7, 31, 12, 0, 0)
    rtk = []
    per = []
    for i in range(num):
        t = start + timedelta(seconds=i / sampling_hz)
        # 极小经纬度增量代表直线移动
        lat = base_lat + i * 1e-4
        lon = base_lon + i * 1e-4
        rtk.append(_Point(t, lat, lon))
        # 感知点做空间偏移（经度上偏移约 offset_m 对应的角度）
        delta_deg = offset_m / 111000.0
        per.append(_Point(t, lat, lon + delta_deg))
    return rtk, per


class _Cfg:
    # 模拟必要参数
    peak_window_duration = 5.0
    min_peak_threshold = 0.7
    segment_duration = 5.0
    quality_threshold_high = 0.8
    quality_threshold_medium = 0.6
    short_trajectory_threshold = 10.0
    sampling_rate = 10.0
    spatial_decay_distance = 5.0


def test_unified_scoring_short_vs_long():
    cfg = _Cfg()
    scorer = UnifiedTrajectoryScorer(cfg)

    # 短轨迹（5s），高质量（偏移1m）应获得较高分（策略 short_trajectory）
    rtk_s, per_s = _build_rtk_and_per(duration_s=5.0, sampling_hz=cfg.sampling_rate, offset_m=1.0)
    seg_short = _Seg('short', per_s)
    score_s, res_s = scorer.unified_scoring(seg_short, rtk_s)
    assert res_s['strategy'] == 'short_trajectory'
    assert score_s > 0.7

    # 长轨迹（20s），中等质量（偏移5m），不一定触发峰值策略，分数应合理（>0.3）
    rtk_l, per_l = _build_rtk_and_per(duration_s=20.0, sampling_hz=cfg.sampling_rate, offset_m=5.0)
    seg_long = _Seg('long', per_l)
    score_l, res_l = scorer.unified_scoring(seg_long, rtk_l)
    assert seg_long.duration > cfg.short_trajectory_threshold
    assert score_l > 0.3


if __name__ == '__main__':
    try:
        test_unified_scoring_short_vs_long()
        print('[OK] 统一评分短/长轨迹回归测试')
    except AssertionError as e:
        print('[FAIL] 统一评分短/长轨迹回归测试:', e)
        import sys
        sys.exit(1)


