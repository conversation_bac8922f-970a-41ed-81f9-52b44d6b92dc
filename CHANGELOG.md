# 变更说明（2025-08-08）

本次改动围绕 CLI 一致性、调用方式重构、报告模板迁移到 Jinja2、文档修正与关键测试补充展开，并完成了测试验证。

## 摘要
- 统一 CLI 参数：改用 `--output-dir`，新增 `--skip-preprocess`；`main.py` 直接函数调用不再篡改 `sys.argv`。
- 报告渲染迁移至 Jinja2：`core/report_generator.py`、`core/batch_report_generator.py` 与 `templates/*.html` 已更新。
- 文档修正与示例统一：更新 `README.md`、`docs/INDEX.md`、`DOCUMENTATION.md`、`BATCH_PROCESSING_GUIDE.md`。
- 新增关键测试：速度单位转换与统一评分回归；报告生成集成测试通过。

## 重要变更

### 1) CLI 参数与入口调用方式
- `main.py` 统一使用 `--output-dir` 指定输出目录，替代旧的 `--output`。
- 新增 `--skip-preprocess`：当输入已为标准 CSV 时可跳过预处理。
- 去除通过修改 `sys.argv` 间接调用的方式，改为直接调用 `core.trajectory_matcher.run_matching(...)`。

受影响文件：
- `main.py`
- `core/trajectory_matcher.py`（新增 `run_matching(...)` 并暴露 CLI 一致参数）

迁移提示：
- 旧命令中的 `--output` 请改为 `--output-dir`。
- 如输入已经是标准 CSV，可追加 `--skip-preprocess` 以提速。

### 2) 报告模板迁移到 Jinja2
- 将原始字符串替换渲染切换为 Jinja2 模板引擎。
- 模板变量使用 `{{ variable }}`，循环/条件使用 `{% ... %}`。
- 新增依赖：`Jinja2>=3.1.0`（已写入 `requirements.txt`）。

受影响文件：
- `core/report_generator.py`
- `core/batch_report_generator.py`
- `templates/accuracy_report_template.html`
- `templates/batch_summary_template.html`
- `requirements.txt`

### 3) 文档修正与 CLI 示例统一
- 统一 CLI 示例为 `--output-dir`，并在适当位置加入 `--skip-preprocess`。
- 修正并行批处理文档指向 `batch_parallel_linux_optimized.py`。
- 移除/更正缺失脚本的引用，补充测试入口说明。

受影响文件：
- `README.md`
- `docs/INDEX.md`
- `DOCUMENTATION.md`
- `BATCH_PROCESSING_GUIDE.md`

### 4) 新增测试（关键回归保障）
- 速度单位转换测试：覆盖新/旧 JSON 格式（km/h → m/s）。
- 统一评分回归测试：覆盖短/长轨迹评分策略稳定性。
- 报告集成测试：验证 CSV/JSON/HTML 报告生成与 Jinja2 渲染。

新增文件：
- `tests/test_speed_unit_conversion.py`
- `tests/test_unified_scoring_regression.py`

已有集成测试：
- `tests/test_accuracy_report_integration.py`

## 使用示例（已在文档同步）

- 单文件处理：
```bash
python main.py \
  --perception data/save_1753355915725.txt \
  --rtk data/31.log \
  --output-dir output/single_analysis \
  --skip-preprocess
```

- 并行批处理（Linux 优化版）：
```bash
python batch_parallel_linux_optimized.py \
  --batch data/batch.csv \
  --config config/unified_config.json \
  --workers 4 \
  --output output/batch_linux
```

- 运行测试：
```bash
python -m tests.test_speed_unit_conversion
python -m tests.test_unified_scoring_regression
python tests/test_accuracy_report_integration.py
```

## 当前测试结论
- 速度单位转换：通过（新/旧 JSON 的 `speed`/`PtcSpeed` 按 km/h 读入并正确转换为 m/s）。
- 统一评分回归：通过（短轨迹触发 `short_trajectory` 策略，长轨迹得分合理）。
- 报告集成/Jinja2：通过（CSV/JSON/HTML 报告生成成功且指标完整，图表数据加载正常）。

## 变更文件清单
- 代码：`main.py`、`core/trajectory_matcher.py`、`core/report_generator.py`、`core/batch_report_generator.py`
- 模板：`templates/accuracy_report_template.html`、`templates/batch_summary_template.html`
- 依赖：`requirements.txt`
- 文档：`README.md`、`docs/INDEX.md`、`DOCUMENTATION.md`、`BATCH_PROCESSING_GUIDE.md`
- 测试：`tests/test_speed_unit_conversion.py`、`tests/test_unified_scoring_regression.py`

## 备注
- 若历史脚本仍使用 `--output`，请替换为 `--output-dir`。
- 已存在 CSV 数据可启用 `--skip-preprocess` 以节省时间。


